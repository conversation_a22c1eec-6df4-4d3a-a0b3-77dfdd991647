# MATRIX UI - <PERSON>lisi Dettagliata Errori di Compilazione

## Sommario
- **Totale errori**: 108
- **Warnings**: 91
- **Errori critici**: 17

## Categorizzazione Errori

### 1. Import e Dependencies Mancanti (15 errori)
- `lapce_editor` crate non trovato (5 errori)
- `thread_local` crate mancante (3 errori)  
- `split` module non trovato (3 errori)
- `floem::window::WindowHandle` non esiste (1 errore)
- `floem::style::Border` non esiste (1 errore)
- Pannelli mancanti: `properties_panel`, `output_panel`, `problems_panel`, `ai_panel` (4 errori)

### 2. API Floem 0.2 Compatibility (25 errori)
- `TextAlign` non esiste in `floem::style` (2 errori)
- `h_stack_from_iter` non importato (6 errori)
- `v_stack_from_iter` non importato (2 errori)
- `v_stack` non importato (1 errore)
- `text_align()` method non esiste (2 errori)
- `text_align_center()` method non esiste (1 errore)
- `set_attr()` method non esiste (1 errore)
- Button API changes (4 errori)
- Event handling changes (6 errori)

### 3. Type Mismatches e ViewTuple (20 errori)
- `(): ViewTuple` non implementato (3 errori)
- Closure non implementa `IntoView` per button (2 errori)
- `on_click` restituisce `()` invece di `EventPropagation` (2 errori)
- Type mismatch tra `LapceIntegration` versions (1 errore)
- `SetupConfig` non implementa `Clone` (1 errore)
- RwSignal trait bounds non soddisfatti (2 errori)
- Path vs PathBuf mismatch (1 errore)
- Opaque type incompatibilities (8 errori)

### 4. Theme e Style System (15 errori)
- `button_bg` field non esiste in `ThemeColors` (2 errori)
- `button_fg` field non esiste in `ThemeColors` (2 errori)
- `hover_bg` field non esiste in `ThemeColors` (2 errori)
- `title_bar_bg` field non esiste in `ThemeColors` (1 errore)
- `get_active_theme_name()` method non esiste (1 errore)
- Theme cloning issues (7 errori)

### 5. Panel System (10 errori)
- `TerminalPanel::new()` richiede 2 argomenti invece di 1 (1 errore)
- Splitter system non implementato (3 errori)
- Panel imports mancanti (4 errori)
- Layout incompatibilities (2 errori)

### 6. Plugin System (8 errori)
- `PluginDescription` fields mancanti: `display_name`, `author`, `repository` (6 errori)
- `PluginHost` non trovato (2 errori)

### 7. Struct Field Issues (15 errori)
- Missing fields in various structs
- Incorrect field names
- Type mismatches in struct initialization

## Piano di Correzione Prioritario

### Fase 1: Dependencies e Import
1. Aggiungere `thread_local` al Cargo.toml
2. Implementare sistema splitter personalizzato
3. Correggere import Floem 0.2
4. Risolvere dipendenza `lapce_editor`

### Fase 2: API Compatibility
1. Aggiornare stack functions import
2. Correggere style API (TextAlign, border, etc.)
3. Aggiornare button e event handling API
4. Correggere window API

### Fase 3: Type System
1. Correggere ViewTuple issues
2. Risolvere type mismatches
3. Aggiornare closure signatures
4. Correggere opaque type returns

### Fase 4: Theme System
1. Aggiornare ThemeColors struct
2. Correggere theme methods
3. Aggiornare style system

### Fase 5: Panel System
1. Implementare pannelli mancanti
2. Correggere constructor signatures
3. Aggiornare layout system

### Fase 6: Plugin System
1. Aggiornare PluginDescription struct
2. Correggere plugin host integration

## Riferimenti API Floem 0.2
- Stack functions: `h_stack_from_iter`, `v_stack_from_iter` esistono
- WindowHandle: non esiste come export pubblico
- Border: esiste come `StrokeWrap` in style
- TextAlign: non esiste, usare text alignment properties
- Button: richiede `IntoView` child, non closure
- Events: `on_click` deve restituire `EventPropagation`

## Note Implementazione
- Mantenere compatibilità con obiettivi ultra/god mode
- Non sacrificare funzionalità per correzioni rapide
- Usare codice sorgente Floem 0.2 locale come riferimento
- Testare dopo ogni correzione incrementale
