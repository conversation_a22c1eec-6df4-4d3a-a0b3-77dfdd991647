//! Professional Layout System for MATRIX_IDE
//!
//! Implementazione ottimizzata del layout professionale per MATRIX IDE
//! utilizzando le API native di Floem 0.2.

use std::{sync::Arc, path::PathBuf};
use floem::{
    reactive::{RwSignal, create_rw_signal, create_memo, SignalGet, SignalUpdate},
    views::{
        container, h_stack, h_stack_from_iter, v_stack, label, empty, tab, scroll, stack,
        Decorators, virtual_list
    },
    style::{CursorStyle, AlignItems, JustifyContent, Position, Display},
    event::{EventListener, Event, EventPropagation},
    View,
    peniko::Color,
};

// In Floem 0.2, split e splitter sono stati rimossi o spostati
// Utilizziamo il nostro sistema di splitter personalizzato
use crate::framework::splitter::{SplitterPosition, SplitterSystem};

use crate::{
    theme::{Theme, ThemeManager},
    error::UiError,
    split,
    components::{
        title_bar::TitleBar,
    },
    // Utilizziamo i componenti dalla versione corretta
    components::file_explorer_improved::FileExplorer,
    components::status_bar::StatusBarManager,
    lapce_bridge_improved::LapceIntegration,
    panels::{
        terminal_panel::TerminalPanel,
        properties_panel::PropertiesPanel,
        output_panel::OutputPanel,
        problems_panel::ProblemsPanel,
        ai_panel::AiPanel,
    },
};

use matrix_core;

/// Tipo pannello
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum PanelType {
    Explorer,
    Terminal,
    Output,
    Problems,
    Properties,
    AiAssistant,
}

/// Posizione pannello
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum PanelPosition {
    Left,
    Right,
    Bottom,
}

/// Stato del pannello
pub struct PanelState {
    /// Tipo del pannello
    pub panel_type: PanelType,

    /// Visibile
    pub visible: RwSignal<bool>,

    /// Posizione
    pub position: PanelPosition,

    /// Titolo
    pub title: String,

    /// Icona
    pub icon: String,
}

/// Layout principale di MATRIX IDE
pub struct MatrixLayout {
    /// Core engine
    core: Arc<matrix_core::Engine>,

    /// Theme manager
    theme_manager: Arc<ThemeManager>,

    /// Lapce integration
    lapce: Arc<LapceIntegration>,

    /// File explorer
    file_explorer: Arc<FileExplorer>,

    /// Terminal panel
    terminal_panel: Arc<TerminalPanel>,

    /// Output panel
    output_panel: Arc<OutputPanel>,

    /// Problems panel
    problems_panel: Arc<ProblemsPanel>,

    /// Properties panel
    properties_panel: Arc<PropertiesPanel>,

    /// AI panel
    ai_panel: Arc<AiPanel>,

    /// File operations engine
    file_operations: Arc<crate::file_operations::FileOperationsEngine>,

    /// Pannelli disponibili
    panels: Vec<PanelState>,

    /// Pannelli a sinistra
    left_panels: Vec<PanelType>,

    /// Pannelli a destra
    right_panels: Vec<PanelType>,

    /// Pannelli in basso
    bottom_panels: Vec<PanelType>,

    /// Pannello attivo a sinistra
    active_left_panel: RwSignal<Option<PanelType>>,

    /// Pannello attivo a destra
    active_right_panel: RwSignal<Option<PanelType>>,

    /// Pannello attivo in basso
    active_bottom_panel: RwSignal<Option<PanelType>>,

    /// Left panel width
    left_panel_width: RwSignal<f64>,

    /// Right panel width
    right_panel_width: RwSignal<f64>,

    /// Bottom panel height
    bottom_panel_height: RwSignal<f64>,

    /// Current opened files
    opened_files: RwSignal<Vec<String>>,

    /// Current active file
    active_file: RwSignal<Option<String>>,

    /// Left panel visible
    left_panel_visible: RwSignal<bool>,

    /// Right panel visible
    right_panel_visible: RwSignal<bool>,

    /// Bottom panel visible
    bottom_panel_visible: RwSignal<bool>,
}

impl MatrixLayout {
    /// Create a new Matrix layout
    pub fn new(
        theme_manager: Arc<ThemeManager>,
        lapce: Arc<LapceIntegration>,
    ) -> Result<Self, UiError> {
        // Inizializza il core engine
        let core = Arc::new(matrix_core::Engine::new()?);

        // Inizializza i componenti di base
        let file_explorer = Arc::new(FileExplorer::new(theme_manager.clone()));
        let terminal_panel = Arc::new(TerminalPanel::new(core.clone(), theme_manager.clone())?);
        let output_panel = Arc::new(OutputPanel::new(core.clone(), theme_manager.clone())?);
        let problems_panel = Arc::new(ProblemsPanel::new(core.clone(), theme_manager.clone())?);
        let properties_panel = Arc::new(PropertiesPanel::new(core.clone(), theme_manager.clone())?);
        let ai_panel = Arc::new(AiPanel::new(core.clone(), theme_manager.clone())?);
        let file_operations = Arc::new(crate::file_operations::FileOperationsEngine::new(core.clone()));

        // Definisci pannelli disponibili
        let panels = vec![
            PanelState {
                panel_type: PanelType::Explorer,
                visible: create_rw_signal(true),
                position: PanelPosition::Left,
                title: "Explorer".to_string(),
                icon: "📁".to_string(),
            },
            PanelState {
                panel_type: PanelType::Terminal,
                visible: create_rw_signal(true),
                position: PanelPosition::Bottom,
                title: "Terminal".to_string(),
                icon: "⌨️".to_string(),
            },
            PanelState {
                panel_type: PanelType::Output,
                visible: create_rw_signal(false),
                position: PanelPosition::Bottom,
                title: "Output".to_string(),
                icon: "📝".to_string(),
            },
            PanelState {
                panel_type: PanelType::Problems,
                visible: create_rw_signal(false),
                position: PanelPosition::Bottom,
                title: "Problems".to_string(),
                icon: "⚠️".to_string(),
            },
            PanelState {
                panel_type: PanelType::Properties,
                visible: create_rw_signal(true),
                position: PanelPosition::Right,
                title: "Properties".to_string(),
                icon: "🔧".to_string(),
            },
            PanelState {
                panel_type: PanelType::AiAssistant,
                visible: create_rw_signal(true),
                position: PanelPosition::Right,
                title: "AI Assistant".to_string(),
                icon: "🤖".to_string(),
            },
        ];

        // Distribuzione iniziale pannelli
        let left_panels = vec![PanelType::Explorer];
        let right_panels = vec![PanelType::Properties, PanelType::AiAssistant];
        let bottom_panels = vec![PanelType::Terminal, PanelType::Output, PanelType::Problems];

        Ok(Self {
            core,
            theme_manager,
            lapce,
            file_explorer,
            terminal_panel,
            output_panel,
            problems_panel,
            properties_panel,
            ai_panel,
            file_operations,
            panels,
            left_panels,
            right_panels,
            bottom_panels,
            active_left_panel: create_rw_signal(Some(PanelType::Explorer)),
            active_right_panel: create_rw_signal(Some(PanelType::Properties)),
            active_bottom_panel: create_rw_signal(Some(PanelType::Terminal)),
            left_panel_width: create_rw_signal(280.0),
            right_panel_width: create_rw_signal(300.0),
            bottom_panel_height: create_rw_signal(250.0),
            opened_files: create_rw_signal(Vec::new()),
            active_file: create_rw_signal(None),
            left_panel_visible: create_rw_signal(true),
            right_panel_visible: create_rw_signal(true),
            bottom_panel_visible: create_rw_signal(true),
        })
    }

    /// Ottieni lo stato di un pannello
    fn get_panel_state(&self, panel_type: PanelType) -> Option<&PanelState> {
        self.panels.iter().find(|p| p.panel_type == panel_type)
    }

    /// Imposta la visibilità di un pannello
    pub fn set_panel_visibility(&self, panel_type: PanelType, visible: bool) {
        if let Some(panel) = self.panels.iter().find(|p| p.panel_type == panel_type) {
            panel.visible.set(visible);

            // Aggiorna anche la visibilità del contenitore in base alla posizione
            match panel.position {
                PanelPosition::Left => {
                    // Se stiamo rendendo visibile questo pannello, assicuriamoci che sia attivo
                    if visible {
                        self.active_left_panel.set(Some(panel_type));
                    }

                    // Controlla se c'è almeno un pannello sinistro visibile
                    let any_visible = self.left_panels.iter()
                        .any(|&p| self.get_panel_state(p)
                                  .map(|s| s.visible.get())
                                  .unwrap_or(false));

                    self.left_panel_visible.set(any_visible);
                },
                PanelPosition::Right => {
                    if visible {
                        self.active_right_panel.set(Some(panel_type));
                    }

                    let any_visible = self.right_panels.iter()
                        .any(|&p| self.get_panel_state(p)
                                  .map(|s| s.visible.get())
                                  .unwrap_or(false));

                    self.right_panel_visible.set(any_visible);
                },
                PanelPosition::Bottom => {
                    if visible {
                        self.active_bottom_panel.set(Some(panel_type));
                    }

                    let any_visible = self.bottom_panels.iter()
                        .any(|&p| self.get_panel_state(p)
                                  .map(|s| s.visible.get())
                                  .unwrap_or(false));

                    self.bottom_panel_visible.set(any_visible);
                },
            }
        }
    }

    /// Toggle della visibilità di un pannello
    pub fn toggle_panel(&self, panel_type: PanelType) {
        if let Some(panel) = self.panels.iter().find(|p| p.panel_type == panel_type) {
            let current = panel.visible.get();
            self.set_panel_visibility(panel_type, !current);
        }
    }

    /// Ottiene il file explorer
    pub fn get_file_explorer(&self) -> Arc<FileExplorer> {
        self.file_explorer.clone()
    }

    /// Crea la vista per l'area dell'editor
    fn create_editor_area(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let opened_files = self.opened_files;
        let active_file = self.active_file;

        // Versione semplificata senza memo per evitare problemi di confronto
        let files = opened_files.get();
        let active = active_file.get();

        // Crea tab per ogni file
        let tabs = if files.is_empty() {
                // No files open
                vec![
                    container(
                        label(|| "Benvenuto in MATRIX IDE")
                            .style(move |s| {
                                s.font_size(18.0)
                                 .color(theme.colors.text_secondary)
                                 .padding(20.0)
                            })
                    )
                    .style(move |s| {
                        s.align_items(Some(AlignItems::Center))
                         .justify_content(Some(JustifyContent::Center))
                         .width_full()
                         .height_full()
                    })
                ]
            } else {
                // Create tabs for each file
                files.iter().map(|file| {
                    let file_name = std::path::Path::new(file)
                        .file_name()
                        .map(|n| n.to_string_lossy().to_string())
                        .unwrap_or_else(|| "Untitled".to_string());

                    let is_active = Some(file) == active.as_ref();
                    let file_path = PathBuf::from(file.clone());

                    container(
                        self.lapce.create_editor_view_for_file(&file_path)
                            .map(|view| view.into_any())
                            .unwrap_or_else(|_| {
                                // Fallback in caso di errore
                                container(
                                    label(move || format!("Errore nell'apertura di {}", file_name))
                                        .style(move |s| {
                                            s.font_size(14.0)
                                             .color(theme.colors.error)
                                             .padding(20.0)
                                        })
                                )
                                .style(move |s| {
                                    s.align_items(Some(AlignItems::Center))
                                     .justify_content(Some(JustifyContent::Center))
                                     .width_full()
                                     .height_full()
                                })
                                .into_any()
                            })
                    )
                    .style(move |s| {
                        s.width_full()
                         .height_full()
                    })
                }).collect()
            } else {
                // Files are open, create tabs for them
                files.iter().map(|file| {
                    let file_name = std::path::Path::new(file)
                        .file_name()
                        .map(|n| n.to_string_lossy().to_string())
                        .unwrap_or_else(|| "Untitled".to_string());

                    let is_active = Some(file) == active.as_ref();
                    let file_path = PathBuf::from(file.clone());

                    container(
                        self.lapce.create_editor_view_for_file(&file_path)
                            .map(|view| view.into_any())
                            .unwrap_or_else(|_| {
                                // Fallback in caso di errore
                                container(
                                    label(move || format!("Errore nell'apertura di {}", file_name))
                                        .style(move |s| {
                                            s.font_size(14.0)
                                             .color(theme.colors.error)
                                             .padding(20.0)
                                        })
                                )
                                .style(move |s| {
                                    s.align_items(Some(AlignItems::Center))
                                     .justify_content(Some(JustifyContent::Center))
                                     .width_full()
                                     .height_full()
                                })
                                .into_any()
                            })
                    )
                    .style(move |s| {
                        if is_active {
                            s.display(Display::Flex)
                        } else {
                            s.display(Display::None)
                        }
                    })
                }).collect()
            };

        // Crea il componente tab - versione semplificata per Floem 0.2
        let active_tab_index = create_rw_signal(0usize);
        let tab_view = tab(
            move || active_tab_index.get(),
            move || opened_files.get(),
            |file: &PathBuf| file.clone(),
            move |file: PathBuf| {
                label(move || file.file_name().unwrap_or_default().to_string_lossy().to_string())
            }
        );

        Ok(
            container(
                v_stack((
                    // Tab bar
                    container(
                        h_stack((
                            scroll(
                                h_stack_from_iter(tab_labels.get())
                            )
                            .style(|s| {
                                s.flex_grow(1.0)
                                 .width(0)  // Per fare in modo che flex_grow funzioni
                            }),

                            // New file button
                            container(
                                label(|| "+".to_string())
                                    .style(move |s| {
                                        s.font_size(18.0)
                                         .color(theme.colors.text_secondary)
                                         .padding_horiz(8.0)
                                    })
                            )
                            .style(move |s| {
                                s.cursor(CursorStyle::Pointer)
                                 .hover(|s| s.background(theme.colors.hover))
                            })
                            .on_click_stop({
                                let this = self.clone();

                                move |_| {
                                    this.create_new_file();
                                }
                            })
                        ))
                    )
                    .style(move |s| {
                        s.width_full()
                         .height(36.0)
                         .background(theme.colors.background_secondary)
                         .border_bottom(1.0)
                         .border_color(theme.colors.border)
                    }),

                    // Editor area
                    container(
                        tab_view
                    )
                    .style(|s| {
                        s.flex_grow(1.0)
                         .width_full()
                    })
                ))
            )
            .style(|s| {
                s.width_full()
                 .height_full()
            })
        )
    }

    /// Crea il pannello sinistro
    fn create_left_panel(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let active_panel = self.active_left_panel;

        // Pannelli sinistri
        let panel_views = self.left_panels.iter().map(|&panel_type| {
            let is_active = active_panel.get() == Some(panel_type);
            let panel_state = self.get_panel_state(panel_type).unwrap();

            // Crea la vista del pannello in base al tipo
            let panel_view: Box<dyn View> = match panel_type {
                PanelType::Explorer => Box::new(self.file_explorer.build()),
                _ => Box::new(
                    container(
                        label(|| format!("Pannello non implementato: {:?}", panel_type))
                            .style(move |s| {
                                s.font_size(14.0)
                                 .color(theme.colors.text_secondary)
                                 .padding(20.0)
                            })
                    )
                )
            };

            container(panel_view)
                .style(move |s| {
                    s.width_full()
                     .height_full()
                     .apply_if(!is_active, |s| s.display(Display::None))
                })
        }).collect::<Vec<_>>();

        // Tab header per pannelli
        let tab_headers = self.left_panels.iter().map(|&panel_type| {
            let is_active = active_panel.get() == Some(panel_type);
            let panel_state = self.get_panel_state(panel_type).unwrap();

            container(
                h_stack((
                    label(move || panel_state.icon.clone())
                        .style(move |s| {
                            s.margin_right(6.0)
                             .font_size(14.0)
                        }),

                    label(move || panel_state.title.clone())
                        .style(move |s| {
                            s.font_size(13.0)
                        })
                ))
                .style(|s| s.items_center())
            )
            .style(move |s| {
                s.padding_horiz(12.0)
                 .padding_vert(6.0)
                 .background(if is_active {
                     theme.colors.background
                 } else {
                     theme.colors.background_secondary
                 })
                 .border_right(1.0)
                 .border_color(theme.colors.border)
                 .cursor(CursorStyle::Pointer)
                 .hover(|s| s.background(theme.colors.hover))
            })
            .on_click_stop({
                let this = self.clone();
                let panel = panel_type;

                move |_| {
                    this.active_left_panel.set(Some(panel));
                }
            })
        }).collect::<Vec<_>>();

        Ok(
            container(
                v_stack((
                    // Tab header
                    container(
                        h_stack_from_iter(tab_headers)
                    )
                    .style(move |s| {
                        s.width_full()
                         .height(36.0)
                         .background(theme.colors.background_secondary)
                         .border_bottom(1.0)
                         .border_color(theme.colors.border)
                    }),

                    // Panel container
                    container(
                        stack(
                            panel_views
                        )
                    )
                    .style(|s| {
                        s.flex_grow(1.0)
                         .width_full()
                    })
                ))
            )
            .style(|s| {
                s.width_full()
                 .height_full()
            })
        )
    }

    /// Crea il pannello destro
    fn create_right_panel(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let active_panel = self.active_right_panel;

        // Pannelli destri
        let panel_views = self.right_panels.iter().map(|&panel_type| {
            let is_active = active_panel.get() == Some(panel_type);
            let panel_state = self.get_panel_state(panel_type).unwrap();

            // Crea la vista del pannello in base al tipo
            let panel_view: Box<dyn View> = match panel_type {
                PanelType::Properties => Box::new(self.properties_panel.build()?),
                PanelType::AiAssistant => Box::new(self.ai_panel.build()?),
                _ => Box::new(
                    container(
                        label(|| format!("Pannello non implementato: {:?}", panel_type))
                            .style(move |s| {
                                s.font_size(14.0)
                                 .color(theme.colors.text_secondary)
                                 .padding(20.0)
                            })
                    )
                )
            };

            container(panel_view)
                .style(move |s| {
                    s.width_full()
                     .height_full()
                     .apply_if(!is_active, |s| s.display(Display::None))
                })
        }).collect::<Vec<_>>();

        // Tab header per pannelli
        let tab_headers = self.right_panels.iter().map(|&panel_type| {
            let is_active = active_panel.get() == Some(panel_type);
            let panel_state = self.get_panel_state(panel_type).unwrap();

            container(
                h_stack((
                    label(move || panel_state.icon.clone())
                        .style(move |s| {
                            s.margin_right(6.0)
                             .font_size(14.0)
                        }),

                    label(move || panel_state.title.clone())
                        .style(move |s| {
                            s.font_size(13.0)
                        })
                ))
                .style(|s| s.items_center())
            )
            .style(move |s| {
                s.padding_horiz(12.0)
                 .padding_vert(6.0)
                 .background(if is_active {
                     theme.colors.background
                 } else {
                     theme.colors.background_secondary
                 })
                 .border_right(1.0)
                 .border_color(theme.colors.border)
                 .cursor(CursorStyle::Pointer)
                 .hover(|s| s.background(theme.colors.hover))
            })
            .on_click_stop({
                let this = self.clone();
                let panel = panel_type;

                move |_| {
                    this.active_right_panel.set(Some(panel));
                }
            })
        }).collect::<Vec<_>>();

        Ok(
            container(
                v_stack((
                    // Tab header
                    container(
                        h_stack_from_iter(tab_headers)
                    )
                    .style(move |s| {
                        s.width_full()
                         .height(36.0)
                         .background(theme.colors.background_secondary)
                         .border_bottom(1.0)
                         .border_color(theme.colors.border)
                    }),

                    // Panel container
                    container(
                        stack(
                            panel_views
                        )
                    )
                    .style(|s| {
                        s.flex_grow(1.0)
                         .width_full()
                    })
                ))
            )
            .style(|s| {
                s.width_full()
                 .height_full()
            })
        )
    }

    /// Crea il pannello inferiore
    fn create_bottom_panel(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let active_panel = self.active_bottom_panel;

        // Pannelli inferiori
        let panel_views = self.bottom_panels.iter().map(|&panel_type| {
            let is_active = active_panel.get() == Some(panel_type);
            let panel_state = self.get_panel_state(panel_type).unwrap();

            // Crea la vista del pannello in base al tipo
            let panel_view: Box<dyn View> = match panel_type {
                PanelType::Terminal => Box::new(self.terminal_panel.build()?),
                PanelType::Output => Box::new(self.output_panel.build()?),
                PanelType::Problems => Box::new(self.problems_panel.build()?),
                _ => Box::new(
                    container(
                        label(|| format!("Pannello non implementato: {:?}", panel_type))
                            .style(move |s| {
                                s.font_size(14.0)
                                 .color(theme.colors.text_secondary)
                                 .padding(20.0)
                            })
                    )
                )
            };

            container(panel_view)
                .style(move |s| {
                    s.width_full()
                     .height_full()
                     .apply_if(!is_active, |s| s.display(Display::None))
                })
        }).collect::<Vec<_>>();

        // Tab header per pannelli
        let tab_headers = self.bottom_panels.iter().map(|&panel_type| {
            let is_active = active_panel.get() == Some(panel_type);
            let panel_state = self.get_panel_state(panel_type).unwrap();

            container(
                h_stack((
                    label(move || panel_state.icon.clone())
                        .style(move |s| {
                            s.margin_right(6.0)
                             .font_size(14.0)
                        }),

                    label(move || panel_state.title.clone())
                        .style(move |s| {
                            s.font_size(13.0)
                        })
                ))
                .style(|s| s.items_center())
            )
            .style(move |s| {
                s.padding_horiz(12.0)
                 .padding_vert(6.0)
                 .background(if is_active {
                     theme.colors.background
                 } else {
                     theme.colors.background_secondary
                 })
                 .border_right(1.0)
                 .border_color(theme.colors.border)
                 .cursor(CursorStyle::Pointer)
                 .hover(|s| s.background(theme.colors.hover))
            })
            .on_click_stop({
                let this = self.clone();
                let panel = panel_type;

                move |_| {
                    this.active_bottom_panel.set(Some(panel));
                }
            })
        }).collect::<Vec<_>>();

        Ok(
            container(
                v_stack((
                    // Tab header
                    container(
                        h_stack_from_iter(tab_headers)
                    )
                    .style(move |s| {
                        s.width_full()
                         .height(36.0)
                         .background(theme.colors.background_secondary)
                         .border_bottom(1.0)
                         .border_color(theme.colors.border)
                    }),

                    // Panel container
                    container(
                        stack(
                            panel_views
                        )
                    )
                    .style(|s| {
                        s.flex_grow(1.0)
                         .width_full()
                    })
                ))
            )
            .style(|s| {
                s.width_full()
                 .height_full()
            })
        )
    }

    /// Chiudi un file
    fn close_file(&self, file: &str) {
        let mut files = self.opened_files.get();
        if let Some(pos) = files.iter().position(|f| f == file) {
            files.remove(pos);
            self.opened_files.set(files.clone());

            // Se era il file attivo, attiva un altro file
            if self.active_file.get().as_ref() == Some(&file.to_string()) {
                if !files.is_empty() {
                    // Attiva il file adiacente o il primo disponibile
                    let new_active = if pos < files.len() {
                        files[pos].clone()
                    } else if !files.is_empty() {
                        files[files.len() - 1].clone()
                    } else {
                        "".to_string()
                    };

                    if !new_active.is_empty() {
                        self.active_file.set(Some(new_active));
                    } else {
                        self.active_file.set(None);
                    }
                } else {
                    self.active_file.set(None);
                }
            }
        }
    }

    /// Crea un nuovo file
    fn create_new_file(&self) {
        let files = self.opened_files.get();
        let mut count = 0;

        // Trova un nome univoco per il nuovo file
        let mut file_name = "Nuovo file".to_string();
        while files.contains(&file_name) {
            count += 1;
            file_name = format!("Nuovo file {}", count);
        }

        // Aggiungi il nuovo file
        let mut new_files = files.clone();
        new_files.push(file_name.clone());

        self.opened_files.set(new_files);
        self.active_file.set(Some(file_name));
    }

    /// Costruisci il layout completo
    pub fn build(&self) -> Result<Box<dyn View>, UiError> {
        let theme = self.theme_manager.get_active_theme()?;

        let title_bar = TitleBar::new(self.theme_manager.clone())?;
        let status_bar = StatusBarManager::new(self.core.clone(), self.theme_manager.clone(), self.file_operations.clone());

        // Layout usando gli splitter di Floem 0.2
        let layout: Box<dyn View> = Box::new(
            v_stack((
                // Title bar
                title_bar.build()?,

                // Main content area with splitters
                split::hsplit(
                    // Left panel (if visible)
                    if self.left_panel_visible.get() {
                        Box::new(self.create_left_panel()?) as Box<dyn View>
                    } else {
                        Box::new(container(empty()))
                    },

                    // Center and right area
                    split::hsplit(
                        // Center area (editor + bottom panel)
                        split::vsplit(
                            // Editor area
                            self.create_editor_area()?,

                            // Bottom panel (if visible)
                            if self.bottom_panel_visible.get() {
                                Box::new(self.create_bottom_panel()?) as Box<dyn View>
                            } else {
                                Box::new(container(empty()))
                            },


                        )
                        .splitter_color(theme.colors.border)
                        .splitter_hover_color(theme.colors.accent)
                        .on_resize({
                            let bottom_panel_height = self.bottom_panel_height.clone();

                            move |sizes| {
                                if sizes.len() >= 2 {
                                    bottom_panel_height.set(sizes[1]);
                                }
                            }
                        }),

                        // Right panel (if visible)
                        if self.right_panel_visible.get() {
                            Box::new(self.create_right_panel()?) as Box<dyn View>
                        } else {
                            Box::new(container(empty()))
                        },


                    )
                    .splitter_color(theme.colors.border)
                    .splitter_hover_color(theme.colors.accent)
                    .on_resize({
                        let right_panel_width = self.right_panel_width.clone();

                        move |sizes| {
                            if sizes.len() >= 2 {
                                right_panel_width.set(sizes[1]);
                            }
                        }
                    }),


                )
                .splitter_color(theme.colors.border)
                .splitter_hover_color(theme.colors.accent)
                .on_resize({
                    let left_panel_width = self.left_panel_width.clone();

                    move |sizes| {
                        if sizes.len() >= 1 {
                            left_panel_width.set(sizes[0]);
                        }
                    }
                })
                .style(|s| {
                    s.flex_grow(1.0)
                     .width_full()
                }),

                // Status bar
                status_bar.build()?,
            ))
            .style(move |s| {
                s.width_full()
                 .height_full()
                 .background(theme.colors.background)
            })
        );

        Ok(layout)
    }
}

/// Crea il layout principale di MATRIX IDE
pub fn create_matrix_layout(
    theme_manager: Arc<ThemeManager>,
    lapce: Arc<LapceIntegration>,
) -> Result<Box<dyn View>, UiError> {
    let layout = MatrixLayout::new(theme_manager, lapce)?;
    layout.build()
}
