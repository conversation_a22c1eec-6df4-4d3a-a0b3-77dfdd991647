use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum UiError {
    #[error("Core error: {0}")]
    CoreError(String),
    
    #[error("IO error: {0}")]
    IoError(String),
    
    #[error("Serialization error: {0}")]
    SerializationError(String),
    
    #[error("Lock error: {0}")]
    LockError(String),
    
    #[error("Editor error: {0}")]
    EditorError(String),
    
    #[error("LSP error: {0}")]
    LspError(String),
    
    #[error("Syntax error: {0}")]
    SyntaxError(String),
    
    #[error("Generic error: {0}")]
    GenericError(String),
    
    #[error("Panel error: {0}")]
    PanelError(String),
    
    #[error("Configuration error: {0}")]
    ConfigurationError(String),
    
    #[error("Validation error: {0}")]
    ValidationError(String),
    
    #[error("Network error: {0}")]
    NetworkError(String),
    
    #[error("File operation error: {0}")]
    FileOperationError(String),
    
    #[error("Theme error: {0}")]
    ThemeError(String),
    
    #[error("Performance error: {0}")]
    PerformanceError(String),

    #[error("Invalid path: {0}")]
    InvalidPath(String),

    #[error("Event error: {0}")]
    EventError(String),

    #[error("Not initialized: {0}")]
    NotInitialized(String),

    #[error("Event bus error: {0}")]
    EventBusError(String),

    #[error("Accessibility error: {0}")]
    AccessibilityError(String),
    
    #[error("State error: {0}")]
    StateError(String),
    
    #[error("Component error: {0}")]
    ComponentError(String),
    
    #[error("Framework error: {0}")]
    FrameworkError(String),
    
    #[error("Terminal error: {0}")]
    TerminalError(String),
    
    #[error("AI error: {0}")]
    AiError(String),
    
    #[error("File operation: {0}")]
    FileOperation(String),
    
    #[error("Task error: {0}")]
    TaskError(String),
    
    #[error("Graph error: {0}")]
    GraphError(String),
    
    #[error("Chain reaction error: {0}")]
    ChainReactionError(String),
    
    #[error("Micro task error: {0}")]
    MicroTaskError(String),
    
    #[error("Dag engine error: {0}")]
    DagEngineError(String),
    
    #[error("Plugin error: {0}")]
    PluginError(String),
    
    #[error("File error: {0}")]
    FileError(String),
    
    #[error("Buffer error: {0}")]
    BufferError(String),
    
    #[error("View error: {0}")]
    ViewError(String),
    
    #[error("Widget error: {0}")]
    WidgetError(String),
    
    #[error("Command error: {0}")]
    CommandError(String),
    
    #[error("Keymap error: {0}")]
    KeymapError(String),
    
    #[error("Workspace error: {0}")]
    WorkspaceError(String),
    
    #[error("Source control error: {0}")]
    SourceControlError(String),
    
    #[error("Terminal error: {0}")]
    Terminal(String),
    
    #[error("Search error: {0}")]
    SearchError(String),
    
    #[error("Problem error: {0}")]
    ProblemError(String),
    
    #[error("Debug error: {0}")]
    DebugError(String),
    
    #[error("Test error: {0}")]
    TestError(String),
    
    #[error("Build error: {0}")]
    BuildError(String),
    
    #[error("Deploy error: {0}")]
    DeployError(String),
    
    #[error("Settings error: {0}")]
    SettingsError(String),
    
    #[error("Extension error: {0}")]
    ExtensionError(String),
    
    #[error("Language error: {0}")]
    LanguageError(String),
    
    #[error("Formatter error: {0}")]
    FormatterError(String),
    
    #[error("Linter error: {0}")]
    LinterError(String),
    
    #[error("Completion error: {0}")]
    CompletionError(String),
    
    #[error("Hover error: {0}")]
    HoverError(String),
    
    #[error("Definition error: {0}")]
    DefinitionError(String),
    
    #[error("Reference error: {0}")]
    ReferenceError(String),
    
    #[error("Rename error: {0}")]
    RenameError(String),
    
    #[error("Code action error: {0}")]
    CodeActionError(String),
    
    #[error("Document symbol error: {0}")]
    DocumentSymbolError(String),
    
    #[error("Workspace symbol error: {0}")]
    WorkspaceSymbolError(String),
    
    #[error("Color provider error: {0}")]
    ColorProviderError(String),
    
    #[error("Folding range error: {0}")]
    FoldingRangeError(String),
    
    #[error("Selection range error: {0}")]
    SelectionRangeError(String),
    
    #[error("Call hierarchy error: {0}")]
    CallHierarchyError(String),
    
    #[error("Type hierarchy error: {0}")]
    TypeHierarchyError(String),
    
    #[error("Semantic tokens error: {0}")]
    SemanticTokensError(String),
    
    #[error("Linked editing range error: {0}")]
    LinkedEditingRangeError(String),
    
    #[error("Moniker error: {0}")]
    MonikerError(String),
    
    #[error("Inlay hint error: {0}")]
    InlayHintError(String),
    
    #[error("Inline value error: {0}")]
    InlineValueError(String),
    
    #[error("Evaluatable expression error: {0}")]
    EvaluatableExpressionError(String),
    
    #[error("Inline completion error: {0}")]
    InlineCompletionError(String),
}

impl From<std::io::Error> for UiError {
    fn from(error: std::io::Error) -> Self {
        UiError::IoError(error.to_string())
    }
}

impl From<serde_json::Error> for UiError {
    fn from(error: serde_json::Error) -> Self {
        UiError::SerializationError(error.to_string())
    }
}

// Generic poison error implementations for common lock types
impl<T> From<std::sync::PoisonError<std::sync::MutexGuard<'_, T>>> for UiError {
    fn from(error: std::sync::PoisonError<std::sync::MutexGuard<'_, T>>) -> Self {
        UiError::LockError(error.to_string())
    }
}

impl<T> From<std::sync::PoisonError<std::sync::RwLockReadGuard<'_, T>>> for UiError {
    fn from(error: std::sync::PoisonError<std::sync::RwLockReadGuard<'_, T>>) -> Self {
        UiError::LockError(error.to_string())
    }
}

impl<T> From<std::sync::PoisonError<std::sync::RwLockWriteGuard<'_, T>>> for UiError {
    fn from(error: std::sync::PoisonError<std::sync::RwLockWriteGuard<'_, T>>) -> Self {
        UiError::LockError(error.to_string())
    }
}

impl From<matrix_core::CoreError> for UiError {
    fn from(error: matrix_core::CoreError) -> Self {
        UiError::CoreError(error.to_string())
    }
}

// All specific error types from matrix_core are handled through the main CoreError conversion above
// These implementations are removed as they reference non-existent types in matrix_core::error
