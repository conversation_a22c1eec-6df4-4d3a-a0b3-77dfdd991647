//! Helper per l'integrazione dei componenti migliorati
//!
//! Questo modulo fornisce utilities robuste per l'integrazione
//! e la gestione degli errori quando si utilizzano i componenti migliorati.

use std::sync::Arc;
use std::path::PathBuf;
use std::fmt;

use crate::error::UiError;
use crate::theme::ThemeManager;
use matrix_core::Engine as CoreEngine;

// Re-export componenti migliorati
pub use crate::app_improved::App;
pub use crate::layout_improved::MatrixLayout;
pub use crate::lapce_bridge_improved::LapceIntegration;
pub use crate::components::title_bar_improved::TitleBar;
pub use crate::components::file_explorer_improved::FileExplorer;
pub use crate::components::simple_test_component::SimpleTestComponent;

/// Errori specifici per i componenti migliorati
#[derive(Debug)]
pub enum ImprovedComponentError {
    /// Errore di inizializzazione
    InitError(String),

    /// Errore di compatibilità API
    ApiCompatError(String),

    /// Errore di integrazione
    IntegrationError(String),

    /// Errore UI generico
    UiError(UiError),

    /// Errore del core engine
    CoreError(String),
}

impl fmt::Display for ImprovedComponentError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::InitError(msg) => write!(f, "Errore di inizializzazione: {}", msg),
            Self::ApiCompatError(msg) => write!(f, "Errore di compatibilità API: {}", msg),
            Self::IntegrationError(msg) => write!(f, "Errore di integrazione: {}", msg),
            Self::UiError(err) => write!(f, "Errore UI: {}", err),
            Self::CoreError(msg) => write!(f, "Errore Core Engine: {}", msg),
        }
    }
}

impl std::error::Error for ImprovedComponentError {}

impl From<UiError> for ImprovedComponentError {
    fn from(err: UiError) -> Self {
        Self::UiError(err)
    }
}

/// Risultato specifico per i componenti migliorati
pub type ImprovedResult<T> = Result<T, ImprovedComponentError>;

/// Configurazione per il setup dei componenti
pub struct SetupConfig {
    /// Tema iniziale
    pub initial_theme: String,

    /// Directory di lavoro
    pub working_directory: Option<PathBuf>,

    /// Timeout per le operazioni (ms)
    pub timeout_ms: u64,

    /// Modalità debug
    pub debug_mode: bool,

    /// Usa mock per test
    pub use_mocks: bool,
}

impl Default for SetupConfig {
    fn default() -> Self {
        Self {
            initial_theme: "dark".to_string(),
            working_directory: None,
            timeout_ms: 5000,
            debug_mode: false,
            use_mocks: false,
        }
    }
}

/// Wrapper per i componenti inizializzati
pub struct ImprovedComponents {
    /// Core engine
    pub core: Arc<CoreEngine>,

    /// Theme manager
    pub theme_manager: Arc<ThemeManager>,

    /// Lapce integration
    pub lapce: Arc<LapceIntegration>,

    /// Layout
    pub layout: Arc<MatrixLayout>,

    /// File explorer
    pub file_explorer: Arc<FileExplorer>,

    /// App
    pub app: App,

    /// Configurazione usata
    pub config: SetupConfig,
}

impl ImprovedComponents {
    /// Crea una nuova istanza di componenti migliorati
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
        lapce: Arc<LapceIntegration>,
        layout: Arc<MatrixLayout>,
        file_explorer: Arc<FileExplorer>,
        app: App,
        config: SetupConfig,
    ) -> Self {
        Self {
            core,
            theme_manager,
            lapce,
            layout,
            file_explorer,
            app,
            config,
        }
    }

    /// Imposta la directory di lavoro
    pub fn set_working_directory(&self, path: PathBuf) -> ImprovedResult<()> {
        if !path.exists() || !path.is_dir() {
            return Err(ImprovedComponentError::InitError(
                format!("Directory non valida: {}", path.display())
            ));
        }

        self.file_explorer.set_root_directory(path.clone())
            .map_err(|e| ImprovedComponentError::from(e))?;

        Ok(())
    }

    /// Avvia l'applicazione in modalità test
    pub fn run_test(&self) -> ImprovedResult<()> {
        if self.config.debug_mode {
            println!("Avvio in modalità test con debug attivo");
        }

        // In un'implementazione reale, qui si avvierebbe l'app in modalità test
        println!("Modalità test avviata");

        Ok(())
    }
}

/// Setup completo dei componenti con gestione errori migliorata
pub fn setup_components(config: SetupConfig) -> ImprovedResult<ImprovedComponents> {
    // Verifica configurazione
    if config.timeout_ms == 0 {
        return Err(ImprovedComponentError::InitError(
            "Timeout non può essere zero".to_string()
        ));
    }

    // Inizializza con gestione errori migliorata
    let core = match CoreEngine::new() {
        Ok(engine) => Arc::new(engine),
        Err(e) => return Err(ImprovedComponentError::CoreError(
            format!("Errore nell'inizializzazione del core engine: {}", e)
        )),
    };

    let theme_manager = match ThemeManager::new() {
        Ok(tm) => Arc::new(tm),
        Err(e) => return Err(ImprovedComponentError::from(e)),
    };

    // Imposta tema iniziale
    match theme_manager.set_active_theme(&config.initial_theme) {
        Ok(_) => {},
        Err(e) => return Err(ImprovedComponentError::from(e)),
    }

    // Inizializza componenti con gestione timeout
    let lapce = match std::panic::catch_unwind(|| {
        LapceIntegration::new(core.clone(), theme_manager.clone())
    }) {
        Ok(Ok(l)) => Arc::new(l),
        Ok(Err(e)) => return Err(ImprovedComponentError::from(e)),
        Err(_) => return Err(ImprovedComponentError::InitError(
            "Panic durante l'inizializzazione di Lapce".to_string()
        )),
    };

    let layout = match std::panic::catch_unwind(|| {
        MatrixLayout::new(theme_manager.clone(), lapce.clone())
    }) {
        Ok(Ok(l)) => Arc::new(l),
        Ok(Err(e)) => return Err(ImprovedComponentError::from(e)),
        Err(_) => return Err(ImprovedComponentError::InitError(
            "Panic durante l'inizializzazione del Layout".to_string()
        )),
    };

    let file_explorer = Arc::new(FileExplorer::new(theme_manager.clone()));

    let app = match App::new() {
        Ok(a) => a,
        Err(e) => return Err(ImprovedComponentError::from(e)),
    };

    // Imposta directory di lavoro se specificata
    if let Some(dir) = &config.working_directory {
        if !dir.exists() || !dir.is_dir() {
            return Err(ImprovedComponentError::InitError(
                format!("Directory di lavoro non valida: {}", dir.display())
            ));
        }

        match file_explorer.set_root_directory(dir.clone()) {
            Ok(_) => {},
            Err(e) => return Err(ImprovedComponentError::from(e)),
        }
    }

    // Crea il wrapper dei componenti
    let components = ImprovedComponents::new(
        core,
        theme_manager,
        lapce,
        layout,
        file_explorer,
        app,
        config.clone(),
    );

    Ok(components)
}

/// Setup semplificato per test con configurazione di default
pub fn setup_for_test() -> ImprovedResult<ImprovedComponents> {
    setup_components(SetupConfig {
        debug_mode: true,
        use_mocks: true,
        ..Default::default()
    })
}

/// Crea solo il componente di test semplice
pub fn create_simple_test_component(theme_manager: Arc<ThemeManager>) -> SimpleTestComponent {
    SimpleTestComponent::new(theme_manager)
}

